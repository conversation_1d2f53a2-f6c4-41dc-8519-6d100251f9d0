
/**
 * mtex-static-templ-plugin 模板工程
 */

import routerItems from './routerItems';
import { ramsCoreDefInit } from 'mtex-rams-core';
ramsCoreDefInit();
//引入全局样式
import '../../style/global.less';
import dataUsageAssistantbaseCompExt from '../common/baseCompExt.js';
import store from '../store/index';

//Plugin应用模块的混入
const pluginMixins = [];
// vuex混入
if (!global_constant.store) {
    global_constant.store = [];
}
global_constant.store.push(store);

global_constant.mounted.push((Vue) => {
    Vue.use(dataUsageAssistantbaseCompExt);
    window.dataUsageAssistantbaseCompExt = dataUsageAssistantbaseCompExt;
    window.$httpRequestList = [];
});

global_constant.scrollStyle = {
    useDefault: false, //是否使用浏览器默认滚动条（只有为false的时候，其它属性才生效）
    width: 10, //滚动条宽度
    trackRadius: '0px', //滚动条轨道的圆角
    trackColor: 'rgb(240, 240, 240, 0.6)', //滚动条轨道的颜色
    thumbRadius: '4px', //滚动条滑块的圆角
    thumbColor: 'rgba(0, 0, 0, 0.2)', //滚动条滑块的颜色
};

for (let i = 0; i < routerItems.length; i++) {
    let routeItem = routerItems[i];
    if (!routeItem.component && !routeItem.jsload) {
        let routeMutiple = routeItem.multiple || false;
        routeItem.component = loadView(routeItem.path, routeMutiple);
    }
    global_constant.routes.push(routeItem);
}

export function loadView(subPath, multiple) {
    return () => {
        systemUtil.popupLoading(true, null, '正在加载应用文件，请稍候...');
        return import( /* webpackChunkName: "view-[request]" */ `../plugin2x/${subPath}Plugin.vue`)
            .then(module => {
                systemUtil.popupLoading(false, null);
                let moduleName = insideModuleGetName();
                let moduleMixin = insideModuleGetMixin();
                if (multiple) {
                    let _module = $.extend(true, {}, module).default;
                    if (!_module.mixins) {
                        _module.mixins = [];
                    }
                    _module.mixins.push(moduleMixin);
                    _module.mixins.push(...pluginMixins);
                    _module.name = moduleName;
                    return _module;
                }
                if (module.default.name != 'js_others') {
                    if (!module.default.mixins) {
                        module.default.mixins = [];
                    }
                    module.default.mixins.push(moduleMixin);
                    module.default.mixins.push(...pluginMixins);
                    module.default.name = moduleName;
                }
                return module;

            }).catch(err => {
                insideModuleLoadError(err);
            });
    };
}