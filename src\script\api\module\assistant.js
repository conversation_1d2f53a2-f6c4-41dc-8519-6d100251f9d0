import aiConfigPost, { aiConfigGet, aiConfigStreamPost } from '../ai-service-config';

// AgentServ API 服务地址配置
const gateWay = '/data-usage-assistant';
const gateWayStream = '/dify-service';

export default {
    // ==================== 聊天接口 ====================

    // 默认聊天接口（使用DeepSeek）
    defaultChat(params, cancelToken = null) {
        return aiConfigPost({
            url: '/v1/chat/completions',
            params: params,
            gateWay: gateWay,
            cancelToken: cancelToken
        });
    },

    // DeepSeek 聊天接口
    deepseekChat(params, cancelToken = null) {
        return aiConfigPost({
            url: '/v1/services/deepseek/chat/completions',
            params: params,
            gateWay: gateWay,
            cancelToken: cancelToken
        });
    },

    // Dify 聊天接口（标准路径）
    difyChat(params, cancelToken = null) {
        return aiConfigPost({
            url: '/v1/services/dify/chat/completions',
            params: params,
            gateWay: gateWay,
            cancelToken: cancelToken
        });
    },

    // Dify 聊天接口（兼容路径）
    difyCompatChat(params, cancelToken = null) {
        return aiConfigPost({
            url: '/v1/dify/chat/completions',
            params: params,
            gateWay: gateWay,
            cancelToken: cancelToken
        });
    },

    // OpenAI/vLLM 聊天接口
    openaiChat(params, cancelToken = null) {
        return aiConfigPost({
            url: '/v1/services/openai/chat/completions',
            params: params,
            gateWay: gateWay,
            cancelToken: cancelToken
        });
    },

    // ==================== 流式聊天接口 ====================

    // Dify 流式聊天接口
    difyStreamChatFlow(params, cancelToken = null, onData = null, onComplete = null, onError = null) {
        // 转换为 Dify 专用的参数格式
        let query = '';
        if (params.query) {
            query = params.query;
        } else if (params.messages && params.messages.length > 0) {
            query = params.messages[params.messages.length - 1].content;
        }

        const difyParams = {
            inputs: {},
            query: query,
            response_mode: 'streaming',
            conversation_id: '',
            user: 'abc-123',
            files: []
        };
        const difyHeaders = {
        };
        if (window.location.hostname.startsWith('127') || window.location.hostname.startsWith('localhost') || window.location.hostname.startsWith('192')) {
            difyHeaders.Authorization = 'Bearer app-j6ewRPc3lcNeQkCfyKK1oAvu';
        } else {
            difyHeaders.Authorization = 'Bearer app-pHmHImH04NMI9IYeF64r2NUM';
        }

        return aiConfigStreamPost({
            // url: '/work/flow/api/chat',
            url: '/v1/chat-messages',
            params: difyParams,
            headers: difyHeaders,
            gateWay: gateWayStream,
            cancelToken: cancelToken,
            onData: onData,
            onComplete: onComplete,
            onError: onError
        });
    },

    // ==================== 服务状态查询 ====================

    // 查看所有可用服务
    getServicesStatus() {
        return aiConfigGet({
            url: '/v1/services/status',
            params: {},
            gateWay: gateWay
        });
    },

    // 默认服务健康检查
    healthCheck() {
        return aiConfigGet({
            url: '/v1/health',
            params: {},
            gateWay: gateWay
        });
    },

    // DeepSeek 服务健康检查
    deepseekHealth() {
        return aiConfigGet({
            url: '/v1/services/deepseek/health',
            params: {},
            gateWay: gateWay
        });
    },

    // Dify 服务健康检查
    difyHealth() {
        return aiConfigGet({
            url: '/v1/services/dify/health',
            params: {},
            gateWay: gateWay
        });
    },

    // OpenAI/vLLM 服务健康检查
    openaiHealth() {
        return aiConfigGet({
            url: '/v1/services/openai/health',
            params: {},
            gateWay: gateWay
        });
    },

    // ==================== 模型列表 ====================

    // 获取默认服务模型列表
    getModels() {
        return aiConfigGet({
            url: '/v1/models',
            params: {},
            gateWay: gateWay
        });
    },

    // 获取 DeepSeek 模型列表
    getDeepseekModels() {
        return aiConfigGet({
            url: '/v1/services/deepseek/models',
            params: {},
            gateWay: gateWay
        });
    },

    // 获取 Dify 模型列表
    getDifyModels() {
        return aiConfigGet({
            url: '/v1/services/dify/models',
            params: {},
            gateWay: gateWay
        });
    },

    // 获取 OpenAI/vLLM 模型列表
    getOpenaiModels() {
        return aiConfigGet({
            url: '/v1/services/openai/models',
            params: {},
            gateWay: gateWay
        });
    },

    // ==================== 便捷方法 ====================

    // 根据服务类型发送聊天消息
    chatByService(service, params, cancelToken = null) {
        const serviceMap = {
            'deepseek': this.deepseekChat,
            'dify': this.difyChat,
            'openai': this.openaiChat,
            'default': this.defaultChat
        };

        const chatMethod = serviceMap[service] || serviceMap['default'];
        return chatMethod.call(this, params, cancelToken);
    },

    // 根据服务类型发送流式聊天消息（仅支持 Dify）
    streamChatByService(service, params, cancelToken = null, onData = null, onComplete = null, onError = null) {
        if (service === 'dify') {
            return this.difyStreamChatFlow(params, cancelToken, onData, onComplete, onError);
        }
        // 其他服务暂不支持流式响应
        const error = new Error(`服务 ${service} 暂不支持流式响应`);
        if (onError) onError(error);
        return Promise.reject(error);

    },

    // 检查指定服务健康状态
    checkServiceHealth(service) {
        const healthMap = {
            'deepseek': this.deepseekHealth,
            'dify': this.difyHealth,
            'openai': this.openaiHealth,
            'default': this.healthCheck
        };

        const healthMethod = healthMap[service] || healthMap['default'];
        return healthMethod.call(this);
    },

    // 获取指定服务的模型列表
    getServiceModels(service) {
        const modelsMap = {
            'deepseek': this.getDeepseekModels,
            'dify': this.getDifyModels,
            'openai': this.getOpenaiModels,
            'default': this.getModels
        };

        const modelsMethod = modelsMap[service] || modelsMap['default'];
        return modelsMethod.call(this);
    }
};
