/**
 * 聊天记录存储管理服务
 * 提供聊天会话的创建、保存、加载、删除等功能
 * 当前使用localStorage实现，预留API接口扩展
 */

// 存储键名常量
const STORAGE_KEYS = {
    CHAT_SESSIONS: 'chat_sessions',
    CURRENT_SESSION_ID: 'current_session_id',
    USER_PREFERENCES: 'user_preferences'
};

// 默认配置
const DEFAULT_CONFIG = {
    maxSessions: 50, // 最大会话数量
    maxMessagesPerSession: 1000, // 每个会话最大消息数
    autoSaveInterval: 3000, // 自动保存间隔(ms)
    titleMaxLength: 50 // 会话标题最大长度
};

/**
 * 生成唯一ID
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * 生成会话标题
 * @param {Array} messages 消息列表
 * @returns {string} 会话标题
 */
function generateSessionTitle(messages) {
    if (!messages || messages.length === 0) {
        return '新对话';
    }

    // 使用第一条用户消息作为标题
    const firstUserMessage = messages.find(msg => msg.type === 'user');
    if (firstUserMessage && firstUserMessage.text) {
        let title = firstUserMessage.text.trim();
        // 移除换行符和多余空格
        title = title.replace(/\s+/g, ' ');
        // 截取前50个字符
        if (title.length > DEFAULT_CONFIG.titleMaxLength) {
            title = title.substring(0, DEFAULT_CONFIG.titleMaxLength) + '...';
        }
        return title;
    }

    return '新对话';
}

/**
 * 聊天存储服务类
 */
class ChatStorageService {
    constructor() {
        this.config = { ...DEFAULT_CONFIG };
        this.autoSaveTimer = null;
        this.pendingSave = false;
    }

    /**
     * 初始化服务
     */
    init() {
        try {
            // 检查localStorage可用性
            if (!this.isLocalStorageAvailable()) {
                console.warn('localStorage不可用，聊天记录将无法保存');
                return false;
            }

            // 清理过期数据
            this.cleanupOldSessions();

            return true;
        } catch (error) {
            console.error('聊天存储服务初始化失败:', error);
            return false;
        }
    }

    /**
     * 检查localStorage是否可用
     */
    isLocalStorageAvailable() {
        try {
            const test = '__localStorage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * 获取所有会话列表
     * @returns {Array} 会话列表
     */
    getAllSessions() {
        try {
            const sessionsData = localStorage.getItem(STORAGE_KEYS.CHAT_SESSIONS);
            if (!sessionsData) {
                return [];
            }

            const sessions = JSON.parse(sessionsData);
            // 按最后更新时间倒序排列
            return sessions.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
        } catch (error) {
            console.error('获取会话列表失败:', error);
            return [];
        }
    }

    /**
     * 获取指定会话
     * @param {string} sessionId 会话ID
     * @returns {Object|null} 会话数据
     */
    getSession(sessionId) {
        try {
            const sessions = this.getAllSessions();
            return sessions.find(session => session.id === sessionId) || null;
        } catch (error) {
            console.error('获取会话失败:', error);
            return null;
        }
    }

    /**
     * 创建新会话
     * @param {Object} options 会话选项
     * @returns {Object} 新会话数据
     */
    createSession(options = {}) {
        const now = new Date().toISOString();
        const session = {
            id: generateId(),
            title: options.title || '新对话',
            messages: [],
            model: options.model || 'DeepSeek',
            createdAt: now,
            updatedAt: now,
            messageCount: 0
        };

        try {
            const sessions = this.getAllSessions();
            sessions.unshift(session);

            // 限制会话数量
            if (sessions.length > this.config.maxSessions) {
                sessions.splice(this.config.maxSessions);
            }

            this.saveSessions(sessions);
            this.setCurrentSessionId(session.id);

            return session;
        } catch (error) {
            console.error('创建会话失败:', error);
            return session;
        }
    }

    /**
     * 保存会话数据
     * @param {string} sessionId 会话ID
     * @param {Object} sessionData 会话数据
     */
    saveSession(sessionId, sessionData) {
        try {
            const sessions = this.getAllSessions();
            const sessionIndex = sessions.findIndex(s => s.id === sessionId);

            if (sessionIndex === -1) {
                console.warn('会话不存在:', sessionId);
                return false;
            }

            // 更新会话数据
            sessions[sessionIndex] = {
                ...sessions[sessionIndex],
                ...sessionData,
                updatedAt: new Date().toISOString()
            };

            // 限制消息数量
            if (sessions[sessionIndex].messages &&
                sessions[sessionIndex].messages.length > this.config.maxMessagesPerSession) {
                sessions[sessionIndex].messages = sessions[sessionIndex].messages
                    .slice(-this.config.maxMessagesPerSession);
            }

            this.saveSessions(sessions);
            return true;
        } catch (error) {
            console.error('保存会话失败:', error);
            return false;
        }
    }

    /**
     * 添加消息到会话
     * @param {string} sessionId 会话ID
     * @param {Object} message 消息对象
     */
    addMessage(sessionId, message) {
        try {
            if (!sessionId || !message) {
                console.warn('无效的参数:', { sessionId, message });
                return false;
            }

            const session = this.getSession(sessionId);
            if (!session) {
                console.warn('会话不存在:', sessionId);
                return false;
            }

            // 验证消息格式
            if (!message.text || !message.type) {
                console.warn('消息格式无效:', message);
                return false;
            }

            // 添加消息ID和时间戳
            const messageWithMeta = {
                id: generateId(),
                timestamp: new Date().toISOString(),
                ...message
            };

            session.messages.push(messageWithMeta);
            session.messageCount = session.messages.length;

            // 自动生成标题（仅在第一条用户消息时）
            if (session.title === '新对话' && message.type === 'user') {
                session.title = generateSessionTitle(session.messages);
            }

            return this.saveSession(sessionId, session);
        } catch (error) {
            console.error('添加消息失败:', error);
            return false;
        }
    }

    /**
     * 删除会话
     * @param {string} sessionId 会话ID
     */
    deleteSession(sessionId) {
        try {
            const sessions = this.getAllSessions();
            const filteredSessions = sessions.filter(s => s.id !== sessionId);

            this.saveSessions(filteredSessions);

            // 如果删除的是当前会话，清除当前会话ID
            if (this.getCurrentSessionId() === sessionId) {
                this.setCurrentSessionId(null);
            }

            return true;
        } catch (error) {
            console.error('删除会话失败:', error);
            return false;
        }
    }

    /**
     * 更新会话标题
     * @param {string} sessionId 会话ID
     * @param {string} title 新标题
     */
    updateSessionTitle(sessionId, title) {
        if (!title || title.trim().length === 0) {
            return false;
        }

        const trimmedTitle = title.trim();
        if (trimmedTitle.length > this.config.titleMaxLength) {
            title = trimmedTitle.substring(0, this.config.titleMaxLength) + '...';
        }

        return this.saveSession(sessionId, { title });
    }

    /**
     * 获取当前会话ID
     */
    getCurrentSessionId() {
        try {
            return localStorage.getItem(STORAGE_KEYS.CURRENT_SESSION_ID);
        } catch (error) {
            console.error('获取当前会话ID失败:', error);
            return null;
        }
    }

    /**
     * 设置当前会话ID
     * @param {string} sessionId 会话ID
     */
    setCurrentSessionId(sessionId) {
        try {
            if (sessionId) {
                localStorage.setItem(STORAGE_KEYS.CURRENT_SESSION_ID, sessionId);
            } else {
                localStorage.removeItem(STORAGE_KEYS.CURRENT_SESSION_ID);
            }
        } catch (error) {
            console.error('设置当前会话ID失败:', error);
        }
    }

    /**
     * 保存会话列表到localStorage
     * @param {Array} sessions 会话列表
     */
    saveSessions(sessions) {
        try {
            localStorage.setItem(STORAGE_KEYS.CHAT_SESSIONS, JSON.stringify(sessions));
        } catch (error) {
            console.error('保存会话列表失败:', error);
            // 如果存储空间不足，尝试清理旧数据
            if (error.name === 'QuotaExceededError') {
                this.cleanupOldSessions(true);
                // 重试保存
                try {
                    localStorage.setItem(STORAGE_KEYS.CHAT_SESSIONS, JSON.stringify(sessions));
                } catch (retryError) {
                    console.error('重试保存失败:', retryError);
                }
            }
        }
    }

    /**
     * 清理旧会话数据
     * @param {boolean} force 是否强制清理
     */
    cleanupOldSessions(force = false) {
        try {
            const sessions = this.getAllSessions();
            let cleanedSessions = sessions;

            if (force || sessions.length > this.config.maxSessions) {
                // 保留最近的会话
                cleanedSessions = sessions.slice(0, this.config.maxSessions);
            }

            // 清理超过30天的会话
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            cleanedSessions = cleanedSessions.filter(session => {
                const sessionDate = new Date(session.updatedAt);
                return sessionDate > thirtyDaysAgo;
            });

            if (cleanedSessions.length !== sessions.length) {
                this.saveSessions(cleanedSessions);
                console.log(`清理了 ${sessions.length - cleanedSessions.length} 个旧会话`);
            }
        } catch (error) {
            console.error('清理旧会话失败:', error);
        }
    }

    /**
     * 搜索会话
     * @param {string} keyword 搜索关键词
     * @returns {Array} 匹配的会话列表
     */
    searchSessions(keyword) {
        if (!keyword || keyword.trim().length === 0) {
            return this.getAllSessions();
        }

        const sessions = this.getAllSessions();
        const lowerKeyword = keyword.toLowerCase();

        return sessions.filter(session => {
            // 搜索标题
            if (session.title.toLowerCase().includes(lowerKeyword)) {
                return true;
            }

            // 搜索消息内容
            return session.messages.some(message =>
                message.text && message.text.toLowerCase().includes(lowerKeyword)
            );
        });
    }

    /**
     * 导出会话数据
     * @param {string} sessionId 会话ID，不传则导出所有
     * @returns {Object} 导出的数据
     */
    exportSessions(sessionId = null) {
        try {
            if (sessionId) {
                const session = this.getSession(sessionId);
                if (session) {
                    return { sessions: [session] };
                }
                return { sessions: [] };
            }
            return {
                sessions: this.getAllSessions(),
                exportTime: new Date().toISOString()
            };

        } catch (error) {
            console.error('导出会话数据失败:', error);
            return { sessions: [] };
        }
    }

    /**
     * 清除所有数据
     */
    clearAllData() {
        try {
            localStorage.removeItem(STORAGE_KEYS.CHAT_SESSIONS);
            localStorage.removeItem(STORAGE_KEYS.CURRENT_SESSION_ID);
            localStorage.removeItem(STORAGE_KEYS.USER_PREFERENCES);
            return true;
        } catch (error) {
            console.error('清除数据失败:', error);
            return false;
        }
    }
}

const chatStorageService = new ChatStorageService();

export default chatStorageService;
