<template>
    <div class="markdown-renderer" :data-message-index="messageIndex">
        <!-- 思考内容独立容器 -->
        <div
            v-if="hasThinkingContent(content)"
            class="thinking-container"
            v-html="getThinkingContent(content, messageIndex)"
        ></div>

        <!-- 正文内容容器 -->
        <div class="markdown-content" v-html="getMainContent(content, messageIndex)"></div>
    </div>
</template>

<script>
const marked = require('marked');
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-light.css';

export default {
    name: 'MarkdownRenderer',
    props: {
        content: {
            type: String,
            required: true,
            default: ''
        },
        messageIndex: {
            type: Number,
            required: true,
            default: 0
        }
    },
    data() {
        return {
            renderedContents: new Map(),
            expandedStates: new Map(),
            thinkingContentCache: new Map(),
            mainContentCache: new Map()
        };
    },
    watch: {
        content: {
            handler(newContent, oldContent) {
                if (newContent !== oldContent) {
                    if (
                        this.hasThinkingContent(oldContent) ||
                        this.hasThinkingContent(newContent)
                    ) {
                        this.clearContentCaches();
                    }
                    this.$nextTick(() => {
                        this.addInteractiveFeatures();
                    });
                }
            },
            immediate: true
        },
        messageIndex: {
            handler(newIndex, oldIndex) {
                if (newIndex !== oldIndex) {
                    this.$nextTick(() => {
                        this.addInteractiveFeatures();
                    });
                }
            }
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.addInteractiveFeatures();
        });
    },
    updated() {
        this.$nextTick(() => {
            this.addInteractiveFeatures();
            this.handleStreamingCompletion();
        });
    },
    beforeDestroy() {
        this.clearAllCaches();
    },
    methods: {
        getRenderedContent(text, messageIndex) {
            if (!text || typeof text !== 'string') {
                return '';
            }

            const isStreaming = this.isStreamingMessage(messageIndex);

            if (isStreaming) {
                return this.getStreamingRenderedContent(text, messageIndex);
            }

            return this.getCompleteRenderedContent(text, messageIndex);
        },

        getStreamingRenderedContent(text, messageIndex) {
            const textLength = text.length;
            // 使用与 hasThinkingContent 一致的检测逻辑
            const hasThinkingTags = this.hasThinkingContent(text);

            // 对于包含思考标签的内容，完全跳过缓存，确保实时正确渲染
            if (hasThinkingTags) {
                try {
                    return this.renderMarkdownOptimized(text, true);
                } catch (error) {
                    console.error('思考内容流式渲染错误:', error);
                    return this.escapeHtml(text);
                }
            }

            // 改进缓存策略：使用更精确的缓存键
            const textHash = this.getSimpleHash(text.substring(0, Math.min(100, text.length)));
            const cacheKey = `stream-${messageIndex}-${Math.floor(textLength / 50)}-${textHash}`;

            // 对于流式内容，减少缓存依赖，确保实时渲染
            if (this.renderedContents.has(cacheKey) && textLength > 200) {
                const cached = this.renderedContents.get(cacheKey);
                if (cached.length <= textLength && cached.textHash === textHash) {
                    return this.incrementalRender(text, cached.content);
                }
            }

            try {
                const renderedHtml = this.renderMarkdownOptimized(text, true);

                // 只缓存较长的内容，避免短内容缓存导致的问题
                if (textLength > 100) {
                    this.renderedContents.set(cacheKey, {
                        content: renderedHtml,
                        length: textLength,
                        textHash: textHash,
                        timestamp: Date.now()
                    });
                }

                if (this.renderedContents.size > 30) {
                    this.cleanupStreamCache(messageIndex);
                }

                return renderedHtml;
            } catch (error) {
                console.error('流式Markdown渲染错误:', error);
                return this.escapeHtml(text);
            }
        },

        getCompleteRenderedContent(text, messageIndex) {
            const textLength = text.length;
            const textHash = this.getTextHash(text);
            const timestamp = Date.now();
            const cacheKey = `complete-${messageIndex}-${textLength}-${textHash}`;

            const cached = this.renderedContents.get(cacheKey);
            if (cached && cached.content && timestamp - cached.timestamp < 30000) {
                return cached.content;
            }

            try {
                const renderedHtml = this.renderMarkdown(text);

                this.renderedContents.set(cacheKey, {
                    content: renderedHtml,
                    timestamp: timestamp
                });

                if (this.renderedContents.size > 50) {
                    const firstKey = this.renderedContents.keys().next().value;
                    this.renderedContents.delete(firstKey);
                }

                return renderedHtml;
            } catch (error) {
                console.error('Markdown渲染错误:', error);
                return this.escapeHtml(text);
            }
        },

        incrementalRender(newText, cachedHtml) {
            const newLength = newText.length;

            if (newLength > 0) {
                try {
                    return this.renderMarkdownOptimized(newText, true);
                } catch (error) {
                    console.error('增量渲染错误:', error);
                    return cachedHtml;
                }
            }

            return cachedHtml;
        },

        renderMarkdownOptimized(text, isStreaming = false) {
            if (!text) return '';

            // 使用与 hasThinkingContent 一致的检测逻辑
            const hasThinkingTags = this.hasThinkingContent(text);

            if (isStreaming) {
                // 如果包含思考标签，直接使用完整渲染，避免被当作普通HTML处理
                if (hasThinkingTags) {
                    return this.renderMarkdownWithThinkingPreprocess(text);
                }

                // 对于不包含思考标签的内容，进行常规的复杂语法检测
                const hasComplexSyntax =
                    /```[\s\S]*?```|`[^`\n]+`|\*\*[^*\n]+\*\*|\*[^*\n]+\*|__[^_\n]+__|_[^_\n]+_|\[.*?\]\(.*?\)|#{1,6}\s+[^\n]+/.test(
                        text
                    );

                // 对于流式内容，如果没有复杂语法且内容较短，使用简单渲染
                if (!hasComplexSyntax && text.length < 500) {
                    return this.escapeHtml(text).replace(/\n/g, '<br>');
                }
            }

            // 对于包含思考标签的内容，使用特殊处理
            if (hasThinkingTags) {
                return this.renderMarkdownWithThinkingPreprocess(text);
            }

            return this.renderMarkdown(text);
        },

        // 专门处理包含思考标签的内容渲染
        renderMarkdownWithThinkingPreprocess(text) {
            if (!text) return '';

            // 预处理：将思考标签临时替换为占位符，避免被当作HTML渲染
            const thinkingPlaceholders = [];
            let processedText = text;

            // 处理 <think> 标签
            processedText = processedText.replace(
                /<think>([\s\S]*?)<\/think>/gi,
                (match, content) => {
                    const placeholder = `__THINKING_PLACEHOLDER_${thinkingPlaceholders.length}__`;
                    thinkingPlaceholders.push({
                        type: 'think',
                        content: content,
                        placeholder: placeholder
                    });
                    return placeholder;
                }
            );

            // 处理 <details> 标签
            processedText = processedText.replace(
                /<details(\s+[^>]*)?>[\s]*<summary(\s+[^>]*)?>([^<]*)<\/summary>([\s\S]*?)<\/details>/gi,
                (match, detailsAttrs, summaryAttrs, summaryText, detailsContent) => {
                    const placeholder = `__THINKING_PLACEHOLDER_${thinkingPlaceholders.length}__`;
                    thinkingPlaceholders.push({
                        type: 'details',
                        title: summaryText.trim() || '详细信息',
                        content: detailsContent,
                        placeholder: placeholder
                    });
                    return placeholder;
                }
            );

            // 处理不完整的思考标签（流式响应中可能出现）
            // 处理未闭合的 <think> 标签
            if (
                /<think(?:\s[^>]*)?>([\s\S]*?)$/i.test(processedText) &&
                !/<\/think>/i.test(processedText)
            ) {
                processedText = processedText.replace(
                    /<think(?:\s[^>]*)?>([\s\S]*?)$/i,
                    (match, content) => {
                        const placeholder = `__THINKING_PLACEHOLDER_${thinkingPlaceholders.length}__`;
                        thinkingPlaceholders.push({
                            type: 'think',
                            content: content,
                            placeholder: placeholder,
                            incomplete: true
                        });
                        return placeholder;
                    }
                );
            }

            // 处理未闭合的 <details> 标签
            if (
                /<details(?:\s[^>]*)?>([\s\S]*?)$/i.test(processedText) &&
                !/<\/details>/i.test(processedText)
            ) {
                processedText = processedText.replace(
                    /<details(?:\s[^>]*)?>([\s\S]*?)$/i,
                    (match, content) => {
                        const placeholder = `__THINKING_PLACEHOLDER_${thinkingPlaceholders.length}__`;
                        // 尝试提取 summary
                        const summaryMatch = content.match(
                            /^[\s]*<summary(?:\s[^>]*)?>([^<]*)<\/summary>([\s\S]*)/i
                        );
                        if (summaryMatch) {
                            thinkingPlaceholders.push({
                                type: 'details',
                                title: summaryMatch[1].trim() || '详细信息',
                                content: summaryMatch[2],
                                placeholder: placeholder,
                                incomplete: true
                            });
                        } else {
                            thinkingPlaceholders.push({
                                type: 'details',
                                title: '详细信息',
                                content: content,
                                placeholder: placeholder,
                                incomplete: true
                            });
                        }
                        return placeholder;
                    }
                );
            }

            // 渲染处理后的文本（不包含思考标签）
            let renderedHtml = this.renderMarkdown(processedText);

            // 将占位符替换回空字符串，因为思考内容会在专门的容器中显示
            thinkingPlaceholders.forEach((item) => {
                renderedHtml = renderedHtml.replace(
                    new RegExp(item.placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
                    ''
                );
            });

            return renderedHtml;
        },

        cleanupStreamCache(currentMessageIndex) {
            const keysToDelete = [];
            for (const key of this.renderedContents.keys()) {
                if (key.startsWith('stream-') && !key.includes(`stream-${currentMessageIndex}-`)) {
                    keysToDelete.push(key);
                }
            }

            keysToDelete.forEach((key) => {
                this.renderedContents.delete(key);
            });
        },

        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        },

        isStreamingMessage(messageIndex) {
            // 优先使用父组件的判断方法
            if (this.$parent && this.$parent.isStreamingMessage) {
                return this.$parent.isStreamingMessage(messageIndex);
            }

            // 备用方案：通过消息内容直接判断
            // 这里可以通过其他方式获取消息状态，比如通过 props 传递
            return false;
        },

        getTextHash(text) {
            if (text.length < 100) {
                return text.length.toString();
            }

            let hash = 0;
            const step = Math.max(1, Math.floor(text.length / 50));
            for (let i = 0; i < text.length; i += step) {
                const char = text.charCodeAt(i);
                hash = (hash << 5) - hash + char;
                hash = hash & hash;
            }
            return hash.toString();
        },

        // 简单哈希函数，用于流式内容缓存
        getSimpleHash(text) {
            if (!text || text.length < 10) {
                if (text) {
                    return text.length.toString();
                }
                return '0';
            }

            let hash = 0;
            for (let i = 0; i < text.length; i++) {
                const char = text.charCodeAt(i);
                hash = (hash << 5) - hash + char;
                hash = hash & hash;
            }
            return Math.abs(hash).toString();
        },

        hasThinkingContent(content) {
            if (!content || typeof content !== 'string') {
                return false;
            }

            // 检查完整的思考标签对
            const hasCompleteThinkingTags =
                /<think[\s\S]*?<\/think>|<details[\s\S]*?<\/details>/i.test(content);

            // 检查不完整的思考标签（流式响应中可能出现）
            const hasIncompleteThinkingTags = /<think(?:\s|>|$)|<details(?:\s|>|$)/i.test(content);

            return hasCompleteThinkingTags || hasIncompleteThinkingTags;
        },

        extractThinkingContent(content) {
            if (!content || typeof content !== 'string') {
                return '';
            }

            const thinkingMatches = [];

            // 提取完整的 <think> 标签内容
            content.replace(/<think>([\s\S]*?)<\/think>/gi, (match, thinkContent) => {
                if (thinkContent.trim()) {
                    thinkingMatches.push({
                        type: 'think',
                        content: thinkContent.trim()
                    });
                }
                return match;
            });

            // 提取完整的 <details> 标签内容
            content.replace(
                /<details(\s+[^>]*)?>[\s]*<summary(\s+[^>]*)?>([^<]*)<\/summary>([\s\S]*?)<\/details>/gi,
                (match, detailsAttrs, summaryAttrs, summaryText, detailsContent) => {
                    if (detailsContent.trim()) {
                        thinkingMatches.push({
                            type: 'details',
                            title: summaryText.trim() || '详细信息',
                            content: detailsContent.trim()
                        });
                    }
                    return match;
                }
            );

            // 处理不完整的思考标签（流式响应中可能出现）
            // 检查是否有未闭合的 <think> 标签
            const incompleteThinkMatch = content.match(/<think(?:\s[^>]*)?>([\s\S]*?)$/i);
            if (incompleteThinkMatch && !/<\/think>/i.test(incompleteThinkMatch[0])) {
                const incompleteContent = incompleteThinkMatch[1];
                if (incompleteContent && incompleteContent.trim()) {
                    thinkingMatches.push({
                        type: 'think',
                        content: incompleteContent.trim(),
                        incomplete: true
                    });
                }
            }

            // 检查是否有未闭合的 <details> 标签
            const incompleteDetailsMatch = content.match(/<details(?:\s[^>]*)?>([\s\S]*?)$/i);
            if (incompleteDetailsMatch && !/<\/details>/i.test(incompleteDetailsMatch[0])) {
                const detailsContent = incompleteDetailsMatch[1];
                // 尝试提取 summary
                const summaryMatch = detailsContent.match(
                    /^[\s]*<summary(?:\s[^>]*)?>([^<]*)<\/summary>([\s\S]*)/i
                );
                if (summaryMatch) {
                    const summaryText = summaryMatch[1];
                    const remainingContent = summaryMatch[2];
                    if (remainingContent && remainingContent.trim()) {
                        thinkingMatches.push({
                            type: 'details',
                            title: summaryText.trim() || '详细信息',
                            content: remainingContent.trim(),
                            incomplete: true
                        });
                    }
                } else if (detailsContent && detailsContent.trim()) {
                    // 没有 summary 的情况
                    thinkingMatches.push({
                        type: 'details',
                        title: '详细信息',
                        content: detailsContent.trim(),
                        incomplete: true
                    });
                }
            }

            return thinkingMatches;
        },

        extractMainContent(content) {
            if (!content || typeof content !== 'string') {
                return content;
            }

            let mainContent = content;

            // 移除完整的 <think> 标签
            mainContent = mainContent.replace(/<think[\s\S]*?<\/think>/gi, '');

            // 移除完整的 <details> 标签
            mainContent = mainContent.replace(/<details[\s\S]*?<\/details>/gi, '');

            // 处理不完整的思考标签（流式响应中可能出现）
            // 如果检测到思考标签开始但没有结束，移除从标签开始到内容结尾的部分
            if (/<think(?:\s|>)/.test(mainContent) && !/<\/think>/i.test(mainContent)) {
                mainContent = mainContent.replace(/<think[\s\S]*$/gi, '');
            }

            if (/<details(?:\s|>)/.test(mainContent) && !/<\/details>/i.test(mainContent)) {
                mainContent = mainContent.replace(/<details[\s\S]*$/gi, '');
            }

            return mainContent.trim();
        },

        getThinkingContent(content, messageIndex) {
            const cacheKey = `thinking-${messageIndex}-${this.getContentHash(content)}`;

            if (this.thinkingContentCache.has(cacheKey)) {
                return this.thinkingContentCache.get(cacheKey);
            }

            const thinkingMatches = this.extractThinkingContent(content);

            if (thinkingMatches.length === 0) {
                return '';
            }

            let renderedHtml = '';

            thinkingMatches.forEach((item, index) => {
                if (item.type === 'think') {
                    renderedHtml += this.renderThinkingBlock(item.content, index, messageIndex);
                } else if (item.type === 'details') {
                    renderedHtml += this.renderDetailsBlock(
                        item.title,
                        item.content,
                        index,
                        messageIndex
                    );
                }
            });

            this.thinkingContentCache.set(cacheKey, renderedHtml);

            // 清理缓存
            if (this.thinkingContentCache.size > 20) {
                const firstKey = this.thinkingContentCache.keys().next().value;
                this.thinkingContentCache.delete(firstKey);
            }

            return renderedHtml;
        },

        getMainContent(content, messageIndex) {
            const cacheKey = `main-${messageIndex}-${this.getContentHash(content)}`;

            if (this.mainContentCache.has(cacheKey)) {
                return this.mainContentCache.get(cacheKey);
            }

            const mainContent = this.extractMainContent(content);

            if (!mainContent) {
                return '';
            }

            try {
                const isStreaming = this.isStreamingMessage(messageIndex);
                let renderedHtml;
                if (isStreaming) {
                    renderedHtml = this.renderMarkdownOptimized(mainContent, true);
                } else {
                    renderedHtml = this.renderMarkdown(mainContent);
                }

                this.mainContentCache.set(cacheKey, renderedHtml);

                // 清理缓存
                if (this.mainContentCache.size > 20) {
                    const firstKey = this.mainContentCache.keys().next().value;
                    this.mainContentCache.delete(firstKey);
                }

                return renderedHtml;
            } catch (error) {
                console.error('主内容渲染失败:', error);
                return this.escapeHtml(mainContent);
            }
        },

        getContentHash(content) {
            if (!content || content.length < 50) {
                if (content) {
                    return content.length.toString();
                }
                return '0';
            }

            let hash = 0;
            const step = Math.max(1, Math.floor(content.length / 30));
            for (let i = 0; i < content.length; i += step) {
                const char = content.charCodeAt(i);
                hash = (hash << 5) - hash + char;
                hash = hash & hash;
            }
            return hash.toString();
        },

        renderThinkingBlock(content, index, messageIndex = null) {
            const safeContent = this.escapeHtml(content);
            const renderedContent = this.renderThinkingMarkdown(content);

            // 判断是否为流式响应，如果是则默认展开
            let isStreaming = false;
            if (messageIndex !== null) {
                isStreaming = this.isStreamingMessage(messageIndex);
            }

            const isExpanded = isStreaming; // 流式时展开，非流式时收起

            let displayStyle;
            if (isExpanded) {
                displayStyle = 'display: block;';
            } else {
                displayStyle = 'display: none;';
            }

            let expandedClass;
            if (isExpanded) {
                expandedClass = ' expanded';
            } else {
                expandedClass = '';
            }

            let iconRotation;
            if (isExpanded) {
                iconRotation = 'transform: rotate(180deg);';
            } else {
                iconRotation = 'transform: rotate(0deg);';
            }

            return `
<div class="thinking-block${expandedClass}" data-thinking-processed="true" data-block-index="${index}" data-is-streaming="${isStreaming}">
    <div class="thinking-header">
        <svg class="thinking-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 12l2 2 4-4"></path>
            <circle cx="12" cy="12" r="9"></circle>
        </svg>
        <span class="thinking-title">思考过程</span>
        <button class="thinking-toggle" title="展开/收起思考过程">
            <svg class="expand-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" style="${iconRotation}">
                <path d="m6 9 6 6 6-6"></path>
            </svg>
        </button>
    </div>
    <div class="thinking-content" style="${displayStyle}" data-thinking-raw="${safeContent}">
        <div class="thinking-markdown-content">${renderedContent}</div>
    </div>
</div>`;
        },

        renderDetailsBlock(title, content, index, messageIndex = null) {
            const safeContent = this.escapeHtml(content);
            const renderedContent = this.renderThinkingMarkdown(content);

            // 判断是否为流式响应，如果是则默认展开
            let isStreaming = false;
            if (messageIndex !== null) {
                isStreaming = this.isStreamingMessage(messageIndex);
            }

            const isExpanded = isStreaming; // 流式时展开，非流式时收起

            let displayStyle;
            if (isExpanded) {
                displayStyle = 'display: block;';
            } else {
                displayStyle = 'display: none;';
            }

            let expandedClass;
            if (isExpanded) {
                expandedClass = ' expanded';
            } else {
                expandedClass = '';
            }

            let iconClass;
            if (isExpanded) {
                iconClass = 'el-icon-arrow-up';
            } else {
                iconClass = 'el-icon-arrow-down';
            }

            return `
<div class="details-block${expandedClass}" data-details-processed="true" data-block-index="${index}" data-is-streaming="${isStreaming}">
    <div class="details-header">
        <span class="details-title">${title || '思考内容'}</span>
        <i class="${iconClass} expand-icon"></i>
    </div>
    <div class="details-content" style="${displayStyle}" data-details-raw="${safeContent}">
        <div class="details-markdown-content">${renderedContent}</div>
    </div>
</div>`;
        },

        saveExpandedStates() {
            try {
                const element = this.$el;
                if (!element) return;

                const expandedBlocks = element.querySelectorAll(
                    '.thinking-block.expanded, .details-block.expanded'
                );
                const currentStates = new Map();

                expandedBlocks.forEach((block) => {
                    const stateKey = this.getBlockStateKey(block);
                    if (stateKey) {
                        currentStates.set(stateKey, true);
                    }
                });

                this.expandedStates = currentStates;
            } catch (error) {
                console.warn('保存展开状态失败:', error);
            }
        },

        getBlockStateKey(block) {
            const isThinkingBlock = block.classList.contains('thinking-block');
            if (!isThinkingBlock && !block.classList.contains('details-block')) return null;

            const content = block.querySelector('.thinking-content, .details-content');
            let rawData = null;
            if (content) {
                rawData =
                    content.getAttribute('data-thinking-raw') ||
                    content.getAttribute('data-details-raw');
            }

            if (!rawData) return null;

            // 添加块索引以确保唯一性
            const blockIndex = block.getAttribute('data-block-index') || '0';
            const messageIndex = (this.$el && this.$el.getAttribute('data-message-index')) || '0';

            let blockType;
            if (isThinkingBlock) {
                blockType = 'thinking';
            } else {
                blockType = 'details';
            }
            return `${messageIndex}-${blockType}-${blockIndex}-${this.getStateHash(rawData)}`;
        },

        restoreExpandedStates() {
            try {
                if (this.expandedStates.size === 0) return;

                this.$nextTick(() => {
                    const element = this.$el;
                    if (!element) return;

                    const allBlocks = element.querySelectorAll('.thinking-block, .details-block');

                    allBlocks.forEach((block) => {
                        const stateKey = this.getBlockStateKey(block);
                        if (!stateKey) return;

                        if (this.expandedStates.has(stateKey)) {
                            const content = block.querySelector(
                                '.thinking-content, .details-content'
                            );
                            const expandIcon = block.querySelector('.expand-icon');

                            if (content) content.style.display = 'block';
                            if (expandIcon) expandIcon.style.transform = 'rotate(180deg)';
                            block.classList.add('expanded');
                        }
                    });
                });
            } catch (error) {
                console.warn('恢复展开状态失败:', error);
            }
        },

        getStateHash(content) {
            if (!content || content.length < 20) {
                return content || '';
            }

            let hash = 0;
            const step = Math.max(1, Math.floor(content.length / 10));
            for (let i = 0; i < content.length; i += step) {
                const char = content.charCodeAt(i);
                hash = (hash << 5) - hash + char;
                hash = hash & hash;
            }
            return hash.toString();
        },

        updateExpandedState(block, isExpanded) {
            try {
                const stateKey = this.getBlockStateKey(block);
                if (!stateKey) return;

                if (isExpanded) {
                    this.expandedStates.set(stateKey, true);
                } else {
                    this.expandedStates.delete(stateKey);
                }
            } catch (error) {
                console.warn('更新展开状态失败:', error);
            }
        },

        renderMarkdown(content) {
            marked.setOptions({
                highlight: (code, language) => {
                    if (
                        language &&
                        this.isValidLanguageIdentifier(language) &&
                        hljs.getLanguage(language)
                    ) {
                        try {
                            return hljs.highlight(language, code).value;
                        } catch (err) {
                            console.warn('代码高亮失败:', err);
                        }
                    }
                    return hljs.highlightAuto(code).value;
                },
                breaks: true,
                gfm: true,
                tables: true,
                sanitize: false,
                renderer: this.createCustomRenderer()
            });

            return marked(content);
        },

        createCustomRenderer() {
            const renderer = new marked.Renderer();

            renderer.code = (code, language) => {
                const validLanguage = language && this.isValidLanguageIdentifier(language);
                let displayLanguage;
                if (validLanguage) {
                    displayLanguage = language.toLowerCase();
                } else {
                    displayLanguage = '代码';
                }

                let highlightedCode;
                if (validLanguage && hljs.getLanguage(language)) {
                    try {
                        highlightedCode = hljs.highlight(language, code).value;
                    } catch (err) {
                        console.warn('代码高亮失败:', err);
                        highlightedCode = hljs.highlightAuto(code).value;
                    }
                } else {
                    highlightedCode = hljs.highlightAuto(code).value;
                }

                return `
                    <div class="code-block-wrapper">
                        <div class="code-block-header">
                            <span class="code-language">${this.escapeHtml(displayLanguage)}</span>
                            <button class="copy-button" title="复制代码">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                </svg>
                                <span class="copy-text">复制</span>
                            </button>
                        </div>
                        <pre><code>${highlightedCode}</code></pre>
                    </div>
                `;
            };

            return renderer;
        },

        isValidLanguageIdentifier(lang) {
            if (!lang || typeof lang !== 'string') {
                return false;
            }

            const normalizedLang = lang.toLowerCase().trim();

            return !(
                normalizedLang.length > 20 ||
                normalizedLang.includes('(') ||
                normalizedLang.includes(')') ||
                normalizedLang.includes('{') ||
                normalizedLang.includes('}') ||
                normalizedLang.includes(' ') ||
                normalizedLang.includes('\n') ||
                normalizedLang.includes(';') ||
                normalizedLang.includes('=') ||
                normalizedLang.includes('[') ||
                normalizedLang.includes(']')
            );
        },

        addCodeCopyButtons(container) {
            const codeBlockWrappers = container.querySelectorAll('.code-block-wrapper');
            codeBlockWrappers.forEach((wrapper) => {
                const copyButton = wrapper.querySelector('.copy-button');
                if (copyButton && !copyButton.hasAttribute('data-listener-added')) {
                    copyButton.addEventListener('click', () => {
                        const code = wrapper.querySelector('pre code');
                        if (code) {
                            this.copyToClipboard(code.textContent);
                            this.showCopySuccess(copyButton);
                        }
                    });
                    copyButton.setAttribute('data-listener-added', 'true');
                }
            });

            const preElements = container.querySelectorAll('pre:not(.code-block-wrapper pre)');
            preElements.forEach((pre) => {
                if (pre.querySelector('.copy-button')) return;

                const copyButton = document.createElement('button');
                copyButton.className = 'copy-button';
                copyButton.innerHTML = `
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                    <span class="copy-text">复制</span>
                `;
                copyButton.title = '复制代码';

                copyButton.addEventListener('click', () => {
                    const code = pre.querySelector('code');
                    if (code) {
                        this.copyToClipboard(code.textContent);
                        this.showCopySuccess(copyButton);
                    }
                });

                pre.style.position = 'relative';
                pre.appendChild(copyButton);
            });
        },

        addTableDownloadButtons(container) {
            const tables = container.querySelectorAll('table');
            tables.forEach((table, index) => {
                if (table.closest('.table-container')) return;

                // 检查表格是否在思考内容容器中，如果是则跳过添加下载按钮
                if (table.closest('.thinking-container')) return;

                const tableContainer = document.createElement('div');
                tableContainer.className = 'table-container';

                const tableWrapper = document.createElement('div');
                tableWrapper.className = 'table-wrapper';

                const downloadButton = document.createElement('button');
                downloadButton.className = 'table-download-button';
                downloadButton.innerHTML = `
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7,10 12,15 17,10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    <span class="download-text">下载</span>
                `;
                downloadButton.title = '下载表格为Excel文件';

                downloadButton.addEventListener('click', () => {
                    this.downloadTableAsExcel(table, `智能助手导出表格_${index + 1}`);
                });

                table.parentNode.insertBefore(tableContainer, table);
                tableWrapper.appendChild(table);
                tableContainer.appendChild(tableWrapper);
                tableContainer.appendChild(downloadButton);
            });
        },

        copyToClipboard(text) {
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text);
            } else {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                document.execCommand('copy');
                textArea.remove();
            }
        },

        showCopySuccess(button) {
            const originalHtml = button.innerHTML;
            button.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="20,6 9,17 4,12"></polyline>
                </svg>
                <span class="copy-text">已复制</span>
            `;
            button.classList.add('copied');

            setTimeout(() => {
                button.innerHTML = originalHtml;
                button.classList.remove('copied');
            }, 2000);
        },

        downloadTableAsExcel(table, filename) {
            const rows = [];
            const headerRow = [];

            const headers = table.querySelectorAll('thead th');
            headers.forEach((th) => {
                headerRow.push(th.textContent.trim());
            });
            if (headerRow.length > 0) {
                rows.push(headerRow);
            }

            const bodyRows = table.querySelectorAll('tbody tr');
            bodyRows.forEach((tr) => {
                const row = [];
                const cells = tr.querySelectorAll('td');
                cells.forEach((td) => {
                    row.push(td.textContent.trim());
                });
                if (row.length > 0) {
                    rows.push(row);
                }
            });

            const csvContent = rows
                .map((row) => row.map((cell) => `"${cell.replace(/"/g, '""')}"`).join(','))
                .join('\n');

            const BOM = '\uFEFF';
            const blob = new Blob([BOM + csvContent], {
                type: 'text/csv;charset=utf-8;'
            });

            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `${filename}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        },

        // 处理流式完成时自动收起思考内容
        handleStreamingCompletion() {
            if (!this.$el) return;

            // 检查当前消息是否还在流式状态
            const isCurrentlyStreaming = this.isStreamingMessage(this.messageIndex);

            // 查找所有标记为流式的思考块
            const streamingBlocks = this.$el.querySelectorAll('[data-is-streaming="true"]');

            streamingBlocks.forEach((block) => {
                // 如果当前消息不再是流式状态，则自动收起思考内容
                if (!isCurrentlyStreaming) {
                    const content = block.querySelector('.thinking-content, .details-content');
                    const header = block.querySelector('.thinking-header, .details-header');
                    const expandIcon = block.querySelector('.expand-icon');

                    if (content && header) {
                        // 收起内容
                        content.style.display = 'none';
                        block.classList.remove('expanded');

                        // 更新图标状态
                        if (expandIcon) {
                            if (expandIcon.tagName === 'svg') {
                                // thinking-block 的 SVG 图标
                                expandIcon.style.transform = 'rotate(0deg)';
                            } else {
                                // details-block 的 element-ui 图标
                                expandIcon.className = expandIcon.className.replace(
                                    'el-icon-arrow-up',
                                    'el-icon-arrow-down'
                                );
                            }
                        }

                        // 移除流式标记
                        block.setAttribute('data-is-streaming', 'false');

                        // 更新展开状态记录
                        this.updateExpandedState(block, false);
                    }
                }
            });
        },

        addInteractiveFeatures() {
            const element = this.$el;
            if (!element) return;

            this.saveExpandedStates();

            this.addCodeCopyButtons(element);
            this.addTableDownloadButtons(element);
            this.addThinkingBlockInteractions(element);

            this.restoreExpandedStates();
        },

        addThinkingBlockInteractions(container) {
            const thinkingBlocks = container.querySelectorAll('.thinking-block, .details-block');
            thinkingBlocks.forEach((block) => {
                const header = block.querySelector('.thinking-header, .details-header');
                const content = block.querySelector('.thinking-content, .details-content');

                if (!header || !content) return;
                if (header.hasAttribute('data-listener-added')) return;

                const markdownContainer = content.querySelector(
                    '.thinking-markdown-content, .details-markdown-content'
                );
                const expandIcon = header.querySelector('.expand-icon, .thinking-toggle');
                let isToggling = false;

                const toggleContent = (event) => {
                    // 阻止事件冒泡，确保点击事件正确处理
                    if (event) {
                        event.preventDefault();
                        event.stopPropagation();
                    }

                    if (isToggling) return;
                    isToggling = true;

                    try {
                        const isVisible = content.style.display !== 'none';

                        if (isVisible) {
                            // 收起思考内容
                            content.style.display = 'none';
                            block.classList.remove('expanded');
                            if (expandIcon) {
                                expandIcon.style.transform = 'rotate(0deg)';
                            }
                            this.updateExpandedState(block, false);
                        } else {
                            // 展开思考内容
                            if (markdownContainer && markdownContainer.innerHTML.trim()) {
                                this.renderThinkingContent(block);
                            }
                            content.style.display = 'block';
                            block.classList.add('expanded');
                            if (expandIcon) {
                                expandIcon.style.transform = 'rotate(180deg)';
                            }
                            this.updateExpandedState(block, true);
                        }
                    } catch (error) {
                        console.error('切换思考过程显示状态失败:', error);
                    } finally {
                        setTimeout(() => {
                            isToggling = false;
                        }, 200);
                    }
                };

                // 为整个header添加点击事件
                header.addEventListener('click', toggleContent);

                // 如果有展开图标，也为其单独添加点击事件（双重保险）
                if (expandIcon) {
                    expandIcon.addEventListener('click', toggleContent);
                }

                header.setAttribute('data-listener-added', 'true');
            });
        },

        renderThinkingContent(block) {
            try {
                const content = block.querySelector('.thinking-content, .details-content');
                if (!content) return;

                const markdownContainer = content.querySelector(
                    '.thinking-markdown-content, .details-markdown-content'
                );

                if (!markdownContainer) return;

                if (!markdownContainer.hasAttribute('data-interactive-added')) {
                    this.$nextTick(() => {
                        this.addCodeCopyButtons(markdownContainer);
                        this.addTableDownloadButtons(markdownContainer);
                        markdownContainer.setAttribute('data-interactive-added', 'true');
                    });
                }
            } catch (error) {
                console.error('渲染思考内容失败:', error);
            }
        },

        renderThinkingMarkdown(content) {
            if (!content || typeof content !== 'string') {
                return '';
            }

            try {
                marked.setOptions({
                    highlight: (code, language) => {
                        if (
                            language &&
                            this.isValidLanguageIdentifier(language) &&
                            hljs.getLanguage(language)
                        ) {
                            try {
                                return hljs.highlight(language, code).value;
                            } catch (err) {
                                console.warn('代码高亮失败:', err);
                            }
                        }
                        return hljs.highlightAuto(code).value;
                    },
                    breaks: true,
                    gfm: true,
                    tables: true,
                    sanitize: false,
                    renderer: this.createCustomRenderer()
                });

                return marked(content);
            } catch (error) {
                console.error('思考内容渲染失败:', error);
                return this.escapeHtml(content);
            }
        },

        clearContentCaches() {
            this.renderedContents.clear();
            this.thinkingContentCache.clear();
            this.mainContentCache.clear();
        },

        clearAllCaches() {
            this.renderedContents.clear();
            this.thinkingContentCache.clear();
            this.mainContentCache.clear();
            this.expandedStates.clear();
        }
    }
};
</script>

<style lang="less">
.markdown-renderer {
    width: 100%;
}

.thinking-container {
    width: fit-content;
    margin-bottom: 16px;
    position: relative;
    background: linear-gradient(
        135deg,
        rgba(99, 102, 241, 0.03) 0%,
        rgba(139, 92, 246, 0.02) 50%,
        rgba(59, 130, 246, 0.03) 100%
    );
    cursor: pointer;

    // 为思考内容提供专门的样式环境
    .thinking-block,
    .details-block {
        &:first-child {
            margin-top: 0;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }
    .thinking-block,
    .details-block {
        margin: 16px 0;
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid #e2e8f0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        transition: all 0.25s ease-out;
        position: relative;

        &.expanded {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
            border-color: #cbd5e1;
        }

        &:hover:not(.expanded) {
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }
    }

    .details-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 4px;
        padding: 6px 12px;
        background: #fff;
        cursor: pointer;
        transition: all 0.2s ease;
        border-radius: 6px 6px 0 0;
        user-select: none;

        &:hover {
            background: #fff;
        }
    }

    .details-title {
        font-weight: 500;
        color: #374151;
        font-size: 14px;
        flex-grow: 1;
    }

    .expand-icon {
        color: #6b7280;
        font-size: 14px;
        transition: transform 0.2s ease;
    }

    .details-block.expanded .expand-icon {
        transform: rotate(90deg);
    }

    .thinking-content,
    .details-content {
        cursor: auto;
        padding: 8px 16px;
        overflow: hidden;
        transition: all 0.25s ease-out;
        border-top: 1px solid #e2e8f0;
        position: relative;
        border-radius: 0 0 12px 12px;
        position: relative;

        p {
            margin: 8px 0;
            padding-left: 0;
            color: #222222;
            line-height: 1.6;

            &:first-child {
                margin-top: 0;
            }

            &:last-child {
                margin-bottom: 0;
            }
        }

        ul,
        ol {
            margin: 8px 0;
            padding-left: 24px;
        }

        li {
            margin: 4px 0;
            color: #555;
        }

        code {
            background: rgba(175, 184, 193, 0.15);
            color: #476582;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
        }

        pre {
            background: rgba(248, 249, 250, 0.8);
            border: 1px solid rgba(175, 184, 193, 0.2);
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            overflow-x: auto;

            code {
                background: none;
                padding: 0;
                border-radius: 0;
            }
        }

        blockquote {
            border-left: 3px solid #3498db;
            margin: 12px 0;
            padding: 8px 12px;
            background: rgba(52, 152, 219, 0.05);
            border-radius: 0 6px 6px 0;
            color: #555;
            font-style: italic;

            p {
                margin: 0;
            }
        }
    }

    .details-block {
        border-left: 3px solid rgba(2, 101, 254, 0.5);

        &.expanded {
            border-left-color: rgba(2, 101, 254, 0.5);
        }
    }
}

.markdown-content {
    width: 100%;
    line-height: 1.6;
    color: #333;

    h1 {
        font-size: 24px;
        font-weight: bold;
        margin: 20px 0 16px 0;
        padding-left: 0;
        color: #2c3e50;
        border-bottom: 2px solid #3498db;
        padding-bottom: 8px;
        line-height: 1.3;

        &:first-child {
            margin-top: 0;
        }
    }

    h2 {
        font-size: 20px;
        font-weight: bold;
        margin: 18px 0 14px 0;
        padding-left: 8px;
        color: #34495e;
        border-bottom: 1px solid #bdc3c7;
        padding-bottom: 6px;
        line-height: 1.3;
    }

    h3 {
        font-size: 18px;
        font-weight: bold;
        margin: 16px 0 12px 0;
        padding-left: 16px;
        color: #34495e;
        line-height: 1.3;
    }

    h4 {
        font-size: 16px;
        font-weight: bold;
        margin: 14px 0 10px 0;
        padding-left: 24px;
        color: #34495e;
        line-height: 1.3;
    }

    h5,
    h6 {
        font-size: 15px;
        font-weight: bold;
        margin: 12px 0 8px 0;
        padding-left: 32px;
        color: #34495e;
        line-height: 1.3;
    }

    p {
        margin: 12px 0;
        padding-left: 8px;
        line-height: 1.7;
        text-indent: 0;
        color: #333;

        &:first-child {
            margin-top: 0;
        }

        &:last-child {
            margin-bottom: 0;
        }

        li & {
            margin: 4px 0;
            padding-left: 0;
        }

        blockquote & {
            padding-left: 0;
        }
    }

    strong {
        font-weight: bold;
        color: #2c3e50;
    }

    em {
        font-style: italic;
        color: #7f8c8d;
    }

    ul,
    ol {
        margin: 12px 0;
        padding-left: 32px;

        li {
            margin: 6px 0;
            line-height: 1.6;
            padding-left: 4px;

            ul,
            ol {
                margin: 6px 0;
                padding-left: 24px;

                li {
                    margin: 4px 0;

                    ul,
                    ol {
                        padding-left: 20px;
                    }
                }
            }
        }
    }

    a {
        color: #0265fe;
        text-decoration: none;
        border-bottom: 1px solid transparent;
        transition: all 0.2s ease;

        &:hover {
            border-bottom-color: #0265fe;
        }
    }

    code {
        background: rgba(175, 184, 193, 0.2);
        color: #476582;
        padding: 3px 6px;
        border-radius: 4px;
        font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata',
            'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
        font-size: 0.9em;
        font-weight: 500;
        border: 1px solid rgba(175, 184, 193, 0.3);
        letter-spacing: 0.025em;
    }

    .code-block-wrapper {
        margin: 16px 0;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        background: rgba(255, 255, 255, 0.6);
        border: 1px solid rgba(175, 184, 193, 0.3);
        backdrop-filter: blur(10px);
    }

    .code-block-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 16px;
        background: rgba(255, 255, 255, 0.8);
        border-bottom: 1px solid rgba(175, 184, 193, 0.2);
        backdrop-filter: blur(5px);

        .code-language {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
                'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }

        .copy-button {
            background: transparent;
            border: none;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 12px;
            color: #0265fe;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.2s ease;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
                'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            font-weight: 500;

            &:hover {
                background: rgba(2, 101, 254, 0.08);
            }

            &.copied {
                color: #28a745;

                &:hover {
                    background: rgba(40, 167, 69, 0.08);
                }
            }

            svg {
                width: 14px;
                height: 14px;
                flex-shrink: 0;
            }

            .copy-text {
                font-weight: 500;
                white-space: nowrap;
            }
        }
    }

    .code-block-wrapper pre {
        background: none;
        border: none;
        border-radius: 0;
        padding: 16px;
        margin: 0;
        overflow-x: auto;
        position: relative;
        box-shadow: none;
        backdrop-filter: none;

        code {
            background: none;
            color: #2d3748;
            padding: 0;
            border: none;
            border-radius: 0;
            font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata',
                'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.5;
            font-weight: 400;
            letter-spacing: 0.025em;
        }
    }

    pre:not(.code-block-wrapper pre) {
        background: rgba(255, 255, 255, 0.6);
        border: 1px solid rgba(175, 184, 193, 0.3);
        border-radius: 10px;
        padding: 16px;
        margin: 16px 0;
        overflow-x: auto;
        position: relative;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        backdrop-filter: blur(10px);

        code {
            background: none;
            color: #2d3748;
            padding: 0;
            border: none;
            border-radius: 0;
            font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata',
                'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.5;
            font-weight: 400;
            letter-spacing: 0.025em;
        }
    }

    pre:not(.code-block-wrapper pre) .copy-button {
        position: absolute;
        top: 12px;
        right: 12px;
        background: transparent;
        border: none;
        border-radius: 6px;
        padding: 5px 10px;
        font-size: 13px;
        color: #0265fe;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.2s ease;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
            'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        font-weight: 500;

        &:hover {
            background: rgba(2, 101, 254, 0.08);
            border-radius: 6px;
        }

        &.copied {
            color: #28a745;

            &:hover {
                background: rgba(40, 167, 69, 0.08);
            }
        }

        svg {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .copy-text {
            font-weight: 500;
            white-space: nowrap;
        }
    }

    .table-container {
        position: relative;
        margin: 16px 0;
    }

    .table-wrapper {
        overflow-x: auto;
        border-radius: 10px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

        &::-webkit-scrollbar {
            height: 8px;
        }

        &::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0);
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            transition: background 0.2s ease;

            &:hover {
                background: rgba(0, 0, 0, 0.3);
            }
        }
    }

    blockquote {
        border-left: 4px solid #0265fe;
        margin: 16px 0;
        padding: 16px 20px;
        background: rgba(2, 101, 254, 0.05);
        border-radius: 0 10px 10px 0;
        color: #555;
        font-style: italic;
        backdrop-filter: blur(5px);

        p {
            margin: 0;
            padding-left: 0;
        }
    }

    table {
        width: 100%;
        min-width: 600px;
        border-collapse: separate;
        border-spacing: 0;
        margin: 0;
        background-color: transparent;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: none;

        thead {
            background: rgba(255, 255, 255, 0.8);
        }

        th {
            padding: 14px 18px;
            text-align: left;
            font-weight: 600;
            color: #2c3e50;
            border-bottom: 1px solid #e8e8e8;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            white-space: nowrap;
            min-width: 100px;
            border-left: none;
            border-right: none;

            &:first-child {
                border-top-left-radius: 10px;
            }

            &:last-child {
                border-top-right-radius: 10px;
            }
        }

        td {
            padding: 14px 18px;
            border-bottom: 1px solid #e8e8e8;
            font-size: 14px;
            line-height: 1.6;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(5px);
            white-space: nowrap;
            min-width: 100px;
            border-left: none;
            border-right: none;
        }

        tbody tr:hover td {
            background: rgba(255, 255, 255, 0.8);
        }

        tbody tr:last-child td {
            border-bottom: none;

            &:first-child {
                border-bottom-left-radius: 10px;
            }

            &:last-child {
                border-bottom-right-radius: 10px;
            }
        }
    }

    .table-download-button {
        position: absolute;
        top: 12px;
        right: 12px;
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(175, 184, 193, 0.3);
        border-radius: 6px;
        padding: 5px 10px;
        font-size: 13px;
        color: #0265fe;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.2s ease;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
            'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        z-index: 15;
        font-weight: 500;

        &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: scale(1.05);
        }

        svg {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .download-text {
            font-weight: 500;
            white-space: nowrap;
        }
    }

    hr {
        border: none;
        height: 1px;
        background: #e8e8e8;
        margin: 16px 0;
    }

    img {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        margin: 8px 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    input[type='checkbox'] {
        margin-right: 8px;
        transform: scale(1.2);
    }
}

// 思考内容专用的 Markdown 样式
.thinking-container {
    // 思考内容中的标题样式优化
    .thinking-markdown-content,
    .details-markdown-content {
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin: 16px 0 12px 0;
            padding-left: 0;
            color: #2c3e50;
            font-weight: 600;
            line-height: 1.4;

            &:first-child {
                margin-top: 0;
            }
        }

        h1 {
            font-size: 20px;
            border-bottom: 2px solid rgba(2, 101, 254, 0.3);
            padding-bottom: 6px;
        }

        h2 {
            font-size: 18px;
            border-bottom: 1px solid rgba(2, 101, 254, 0.2);
            padding-bottom: 4px;
        }

        h3 {
            font-size: 16px;
            color: #34495e;
        }

        h4,
        h5,
        h6 {
            font-size: 15px;
            color: #34495e;
        }

        // 段落样式优化
        p {
            margin: 12px 0;
            padding: 0;
            line-height: 1.7;
            color: #2c3e50;
            font-size: 14px;

            &:first-child {
                margin-top: 0;
            }

            &:last-child {
                margin-bottom: 0;
            }
        }

        // 列表样式优化
        ul,
        ol {
            margin: 12px 0;
            padding-left: 20px;

            li {
                margin: 6px 0;
                line-height: 1.6;
                color: #34495e;
                font-size: 14px;

                p {
                    margin: 4px 0;
                    display: inline;
                }

                // 嵌套列表
                ul,
                ol {
                    margin: 6px 0;
                    padding-left: 16px;
                }
            }
        }

        // 代码块专用样式
        .code-block-wrapper {
            margin: 16px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            background: rgba(248, 249, 250, 0.95);
            border: 1px solid rgba(2, 101, 254, 0.1);

            .code-block-header {
                background: linear-gradient(
                    135deg,
                    rgba(2, 101, 254, 0.05),
                    rgba(248, 250, 252, 0.9)
                );
                border-bottom: 1px solid rgba(2, 101, 254, 0.1);
                padding: 8px 12px;

                .code-language {
                    font-size: 11px;
                    color: #0265fe;
                    font-weight: 600;
                }

                .copy-button {
                    background: transparent;
                    border: none;
                    border-radius: 6px;
                    padding: 4px 8px;
                    font-size: 12px;
                    color: #0265fe;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    transition: all 0.2s ease;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
                        'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
                    font-weight: 500;

                    &:hover {
                        background: rgba(2, 101, 254, 0.08);
                    }

                    &.copied {
                        color: #28a745;

                        &:hover {
                            background: rgba(40, 167, 69, 0.08);
                        }
                    }

                    svg {
                        width: 14px;
                        height: 14px;
                        flex-shrink: 0;
                    }

                    .copy-text {
                        font-weight: 500;
                        white-space: nowrap;
                    }
                }
            }

            pre {
                background: none;
                margin: 0;
                padding: 12px;

                code {
                    font-size: 13px;
                    line-height: 1.4;
                }
            }
        }

        // 行内代码样式
        code:not(.code-block-wrapper code) {
            background: rgba(2, 101, 254, 0.08);
            color: #0265fe;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;
            border: 1px solid rgba(2, 101, 254, 0.15);
        }

        // 独立代码块样式
        pre:not(.code-block-wrapper pre) {
            background: rgba(248, 249, 250, 0.95);
            border: 1px solid rgba(2, 101, 254, 0.1);
            border-radius: 8px;
            padding: 12px;
            margin: 16px 0;
            overflow-x: auto;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

            code {
                background: none;
                color: #2c3e50;
                font-size: 13px;
                line-height: 1.4;
            }
        }

        // 引用块样式
        blockquote {
            border-left: 3px solid #0265fe;
            margin: 16px 0;
            padding: 12px 16px;
            background: linear-gradient(135deg, rgba(2, 101, 254, 0.05), rgba(240, 248, 255, 0.8));
            border-radius: 0 8px 8px 0;
            color: #34495e;
            font-style: italic;
            box-shadow: 0 2px 8px rgba(2, 101, 254, 0.05);

            p {
                margin: 0;
                color: inherit;
            }

            // 嵌套引用
            blockquote {
                margin: 8px 0;
                padding: 8px 12px;
                border-left-color: rgba(2, 101, 254, 0.6);
            }
        }

        // 链接样式
        a {
            color: #0265fe;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: all 0.2s ease;
            font-weight: 500;

            &:hover {
                border-bottom-color: #0265fe;
                background: rgba(2, 101, 254, 0.05);
                padding: 1px 2px;
                border-radius: 2px;
            }
        }

        // 强调文本样式
        strong {
            color: #2c3e50;
            font-weight: 600;
        }

        em {
            color: #34495e;
            font-style: italic;
        }

        // 分割线样式
        hr {
            border: none;
            height: 1px;
            background: linear-gradient(
                90deg,
                transparent 0%,
                rgba(2, 101, 254, 0.3) 50%,
                transparent 100%
            );
            margin: 20px 0;
        }

        // 表格样式（如果思考内容中有表格）
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin: 16px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(2, 101, 254, 0.1);

            th {
                background: linear-gradient(
                    135deg,
                    rgba(2, 101, 254, 0.08),
                    rgba(240, 248, 255, 0.9)
                );
                color: #2c3e50;
                font-weight: 600;
                padding: 8px 12px;
                font-size: 13px;
                border-bottom: 1px solid rgba(2, 101, 254, 0.1);
            }

            td {
                padding: 8px 12px;
                font-size: 13px;
                border-bottom: 1px solid rgba(2, 101, 254, 0.05);
                background: rgba(255, 255, 255, 0.8);
            }

            tr:hover td {
                background: rgba(240, 248, 255, 0.6);
            }

            tr:last-child td {
                border-bottom: none;
            }
        }

        // 图片样式
        img {
            max-width: 100%;
            height: auto;
            border-radius: 6px;
            margin: 12px 0;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(2, 101, 254, 0.1);
        }

        // 任务列表样式
        input[type='checkbox'] {
            margin-right: 6px;
            transform: scale(1.1);
            accent-color: #0265fe;
        }

        // 响应式优化
        @media (max-width: 768px) {
            h1 {
                font-size: 18px;
            }
            h2 {
                font-size: 16px;
            }
            h3 {
                font-size: 15px;
            }
            h4,
            h5,
            h6 {
                font-size: 14px;
            }

            p,
            li {
                font-size: 13px;
                line-height: 1.6;
            }

            .code-block-wrapper,
            pre:not(.code-block-wrapper pre) {
                margin: 12px 0;

                code {
                    font-size: 12px;
                }
            }

            blockquote {
                margin: 12px 0;
                padding: 8px 12px;
            }

            table {
                font-size: 12px;

                th,
                td {
                    padding: 6px 8px;
                }
            }
        }
    }
}
</style>
