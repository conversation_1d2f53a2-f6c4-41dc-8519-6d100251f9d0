/**
 * 文本点击复制
 * @param {*} text 需要复制的文本
 * @returns 
 */
export function copyText(text) {
    text = text.trim();
    return new Promise((resolve, reject) => {
        if (navigator.clipboard) {
            navigator.clipboard
                .writeText(text)
                .then(() => resolve('复制成功'))
                .catch((err) => reject('复制失败，请手动复制'));
        }
        else {
            try {
                const textarea = document.createElement('textarea');
                textarea.value = text;
                textarea.style.position = 'fixed';
                document.body.appendChild(textarea);
                textarea.select();

                const success = document.execCommand('copy');
                document.body.removeChild(textarea);

                if (success) {
                    resolve('复制成功');
                } else {
                    reject('复制失败，请手动复制');
                }
            } catch (err) {
                reject('复制失败，请手动复制');
            }
        }
    });
}
