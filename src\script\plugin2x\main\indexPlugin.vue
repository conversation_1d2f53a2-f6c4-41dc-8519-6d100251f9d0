<template>
    <div class="main-index-page dataUsageAssistant-theme">
        <HomePage class="index-page-content" />
    </div>
</template>
<script>
import { styleMixin } from '@/script/mixin/styleMixin';
import HomePage from './components/HomePage.vue';

export default {
    name: 'MainIndex',
    mixins: [styleMixin],
    components: {
        HomePage
    },
    methods: {}
};
</script>
<style scoped lang="less">
.main-index-page {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    .index-page-content {
        width: 100%;
        min-height: 0;
        flex: 1;
    }
}
</style>
